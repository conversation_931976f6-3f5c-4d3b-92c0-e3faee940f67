#!/usr/bin/env python3
"""
Test script for attendance QR code functionality
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
sys.path.append('/Users/<USER>/Documents/code/lms')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

from django.utils import timezone
from attendance.utils import qr_generator
import json
from django.core.signing import Signer

def test_qr_validation():
    """Test QR code validation functions"""
    print("Testing QR Code Validation...")
    
    # Test 1: Valid QR code
    print("\n1. Testing valid QR code:")
    qr_data = {
        'student_id': 123,
        'generated_at': timezone.now().isoformat(),
        'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
        'type': 'attendance'
    }
    
    signer = Signer(salt='attendance_qr')
    signed_content = signer.sign(json.dumps(qr_data))
    
    result = qr_generator.validate_qr_content(signed_content)
    print(f"   Valid: {result['valid']}")
    print(f"   Status: {result['status']}")
    print(f"   Student ID: {result['student_id']}")
    assert result['valid'] == True
    assert result['status'] == 'present'
    assert result['student_id'] == 123
    print("   ✓ Valid QR test passed")
    
    # Test 2: Expired QR code
    print("\n2. Testing expired QR code:")
    expired_qr_data = {
        'student_id': 456,
        'generated_at': (timezone.now() - timedelta(days=35)).isoformat(),
        'expires_at': (timezone.now() - timedelta(days=5)).isoformat(),
        'type': 'attendance'
    }
    
    expired_signed_content = signer.sign(json.dumps(expired_qr_data))
    expired_result = qr_generator.validate_qr_content(expired_signed_content)
    print(f"   Valid: {expired_result['valid']}")
    print(f"   Status: {expired_result['status']}")
    print(f"   Student ID: {expired_result['student_id']}")
    assert expired_result['valid'] == False
    assert expired_result['status'] == 'expired'
    assert expired_result['student_id'] == 456
    print("   ✓ Expired QR test passed")
    
    # Test 3: Invalid QR code
    print("\n3. Testing invalid QR code:")
    invalid_result = qr_generator.validate_qr_content('invalid_qr_content')
    print(f"   Valid: {invalid_result['valid']}")
    print(f"   Status: {invalid_result['status']}")
    print(f"   Student ID: {invalid_result['student_id']}")
    assert invalid_result['valid'] == False
    assert invalid_result['status'] == 'invalid'
    assert invalid_result['student_id'] == None
    print("   ✓ Invalid QR test passed")
    
    # Test 4: Wrong type QR code
    print("\n4. Testing wrong type QR code:")
    wrong_type_data = {
        'student_id': 789,
        'generated_at': timezone.now().isoformat(),
        'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
        'type': 'registration'  # Wrong type
    }
    
    wrong_type_signed = signer.sign(json.dumps(wrong_type_data))
    wrong_type_result = qr_generator.validate_qr_content(wrong_type_signed)
    print(f"   Valid: {wrong_type_result['valid']}")
    print(f"   Status: {wrong_type_result['status']}")
    print(f"   Student ID: {wrong_type_result['student_id']}")
    assert wrong_type_result['valid'] == False
    assert wrong_type_result['status'] == 'invalid'
    assert wrong_type_result['student_id'] == None
    print("   ✓ Wrong type QR test passed")
    
    print("\n✅ All QR validation tests passed!")

def test_qr_generation():
    """Test QR code generation (mock student)"""
    print("\nTesting QR Code Generation...")
    
    # Create a mock student object
    class MockStudent:
        def __init__(self):
            self.id = 999
            self.name = "Test Student"
    
    mock_student = MockStudent()
    
    try:
        qr_base64 = qr_generator.generate_student_qr(mock_student)
        if qr_base64:
            print(f"   ✓ QR code generated successfully (length: {len(qr_base64)} chars)")
            print(f"   ✓ QR code starts with: {qr_base64[:50]}...")
        else:
            print("   ✗ QR code generation failed")
            return False
    except Exception as e:
        print(f"   ✗ QR code generation error: {e}")
        return False
    
    print("✅ QR generation test passed!")
    return True

if __name__ == "__main__":
    print("🔍 Testing Attendance QR Code Functionality")
    print("=" * 50)
    
    try:
        test_qr_validation()
        test_qr_generation()
        print("\n🎉 All tests passed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
